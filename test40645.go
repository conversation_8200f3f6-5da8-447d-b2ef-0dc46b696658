package main

import (
	"fmt"
	"log"
)

// abs 返回整数的绝对值
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

func main() {
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)

	log.Println("开始分析40645结果的可能性...")

	// 从game.go中的常量
	const (
		ICON_BLANK       = 0
		ICON_BAR_1       = 1
		ICON_BAR_2       = 2
		ICON_BAR_3       = 3
		ICON_7           = 4
		ICON_BONUS       = 5
		ICON_BONUS_SUPER = 6
		ICON_FREE        = 7
		ICON_WILD_2X     = 8
		ICON_WILD_3X     = 9
		ICON_WILD_5X     = 10
		ICON_WILD        = 11
	)

	// 赔率信息
	odd_info := []int{1, 3, 5, 8, 10, 40, 135, 625}

	log.Println("分析40645的数学构成:")

	// 最高赔率是625 (ICON_7的赔率，从odd_info[4]获得)
	// 最高倍数组合：3个WILD_5X = 5^3 = 125倍
	maxSingleRound := 625 * 125
	log.Printf("单轮最大可能结果: %d", maxSingleRound)

	// 40645需要多少轮这样的结果
	roundsNeeded := 40645 / maxSingleRound
	remainder := 40645 % maxSingleRound
	log.Printf("达到40645需要: %d轮最大结果 + %d", roundsNeeded, remainder)

	// 免费游戏最多10轮，看看是否可能
	if roundsNeeded <= 10 {
		log.Printf("理论上可能通过免费游戏达到40645")

		// 计算需要的具体组合
		if remainder > 0 {
			// 需要找到能产生remainder的组合
			log.Printf("需要找到能产生%d结果的组合", remainder)

			// 尝试不同的赔率和倍数组合来找到40645
			log.Printf("寻找能产生%d的赔率和倍数组合:", remainder)
			found := false

			// 尝试所有可能的倍数组合
			for mul2x := 0; mul2x <= 3; mul2x++ { // WILD_2X 最多3个
				for mul3x := 0; mul3x <= 3; mul3x++ { // WILD_3X 最多3个
					for mul5x := 0; mul5x <= 3; mul5x++ { // WILD_5X 最多3个
						for mulWild := 0; mulWild <= 3; mulWild++ { // WILD 最多3个
							totalMul := 1
							for i := 0; i < mul2x; i++ {
								totalMul *= 2
							}
							for i := 0; i < mul3x; i++ {
								totalMul *= 3
							}
							for i := 0; i < mul5x; i++ {
								totalMul *= 5
							}
							for i := 0; i < mulWild; i++ {
								totalMul *= 2
							}

							// 检查每个赔率
							for oddsIdx, odds := range odd_info {
								result := odds * totalMul
								if result == remainder {
									log.Printf("找到匹配组合: 赔率%d(图标%d) * 倍数%d = %d", odds, oddsIdx, totalMul, result)
									log.Printf("倍数构成: WILD_2X=%d, WILD_3X=%d, WILD_5X=%d, WILD=%d", mul2x, mul3x, mul5x, mulWild)
									found = true
								}
							}
						}
					}
				}
			}

			if !found {
				log.Printf("未找到精确匹配40645的组合")
			}
		}

		// 构造一个可能的图标序列
		log.Println("构造可能的图标序列:")

		// 主游戏：触发免费游戏
		mainGame := []int{ICON_BONUS, ICON_7, ICON_BONUS, ICON_BONUS, ICON_7, ICON_7, ICON_BONUS, ICON_7, ICON_7}
		fmt.Printf("主游戏图标: ")
		for i, icon := range mainGame {
			if i > 0 {
				fmt.Printf(",")
			}
			fmt.Printf("%d", icon)
		}
		fmt.Printf("\n")

		// 免费游戏：高倍数组合
		for round := 0; round < roundsNeeded; round++ {
			freeGame := []int{ICON_7, ICON_WILD_5X, ICON_7, ICON_7, ICON_WILD_5X, ICON_7, ICON_7, ICON_WILD_5X, ICON_7}
			fmt.Printf("免费游戏第%d轮: ", round+1)
			for i, icon := range freeGame {
				if i > 0 {
					fmt.Printf(",")
				}
				fmt.Printf("%d", icon)
			}
			fmt.Printf("\n")
		}

		// 如果有余数，添加一个产生余数的轮次
		if remainder > 0 {
			// 简化：使用较小的倍数
			remainderGame := []int{ICON_7, ICON_WILD, ICON_7, ICON_7, ICON_WILD, ICON_7, ICON_7, ICON_WILD, ICON_7}
			fmt.Printf("余数轮次: ")
			for i, icon := range remainderGame {
				if i > 0 {
					fmt.Printf(",")
				}
				fmt.Printf("%d", icon)
			}
			fmt.Printf("\n")
		}

		// 输出完整的图标序列
		fmt.Printf("完整图标序列: ")
		iconCount := 0

		// 主游戏
		for _, icon := range mainGame {
			if iconCount > 0 {
				fmt.Printf(",")
			}
			fmt.Printf("%d", icon)
			iconCount++
		}

		// 免费游戏
		for round := 0; round < roundsNeeded; round++ {
			freeGame := []int{ICON_7, ICON_WILD_5X, ICON_7, ICON_7, ICON_WILD_5X, ICON_7, ICON_7, ICON_WILD_5X, ICON_7}
			for _, icon := range freeGame {
				fmt.Printf(",%d", icon)
				iconCount++
			}
		}

		// 余数轮次
		if remainder > 0 {
			remainderGame := []int{ICON_7, ICON_WILD, ICON_7, ICON_7, ICON_WILD, ICON_7, ICON_7, ICON_WILD, ICON_7}
			for _, icon := range remainderGame {
				fmt.Printf(",%d", icon)
				iconCount++
			}
		}

		fmt.Printf("\n")
		log.Printf("总图标数: %d", iconCount)

	} else {
		log.Printf("即使10轮免费游戏也无法达到40645")
	}
}
