package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"strconv"
)

func main() {
	// 读取JSON文件
	data, err := ioutil.ReadFile("game_result_data.json")
	if err != nil {
		log.Fatal("读取文件失败:", err)
	}

	// 解析JSON
	var mMapResult map[string][]string
	err = json.Unmarshal(data, &mMapResult)
	if err != nil {
		log.Fatal("解析JSON失败:", err)
	}

	// 统计各区间的数据
	var invalidResults []string
	validCount1 := 0 // 区间1中满足条件的结果数
	validCount2 := 0 // 区间2中满足条件的结果数
	validCount3 := 0 // 区间3中满足条件的结果数

	totalCount1 := 0 // 区间1总记录数
	totalCount2 := 0 // 区间2总记录数
	totalCount3 := 0 // 区间3总记录数

	for resultStr, records := range mMapResult {
		result, err := strconv.Atoi(resultStr)
		if err != nil {
			log.Printf("无法解析结果值: %s", resultStr)
			continue
		}

		recordCount := len(records)

		if result >= 1 && result <= 2000 {
			totalCount1 += recordCount
			if recordCount >= 200 {
				validCount1++
			} else {
				invalidResults = append(invalidResults, fmt.Sprintf("结果%d只有%d条记录(需要200条)", result, recordCount))
			}
		} else if result > 2000 && result <= 5000 {
			totalCount2 += recordCount
			if recordCount >= 100 {
				validCount2++
			} else {
				invalidResults = append(invalidResults, fmt.Sprintf("结果%d只有%d条记录(需要100条)", result, recordCount))
			}
		} else if result > 5000 {
			totalCount3 += recordCount
			if recordCount >= 50 {
				validCount3++
			} else {
				invalidResults = append(invalidResults, fmt.Sprintf("结果%d只有%d条记录(需要50条)", result, recordCount))
			}
		}
	}

	fmt.Printf("数据统计:\n")
	fmt.Printf("区间1 (1-2000): 总共%d条记录，%d个结果满足条件(每个≥200条)\n", totalCount1, validCount1)
	fmt.Printf("区间2 (2000-5000): 总共%d条记录，%d个结果满足条件(每个≥100条)\n", totalCount2, validCount2)
	fmt.Printf("区间3 (>5000): 总共%d条记录，%d个结果满足条件(每个≥50条)\n", totalCount3, validCount3)

	// 检查是否所有结果都满足条件
	allValid := len(invalidResults) == 0

	fmt.Printf("\n验证结果:\n")
	fmt.Printf("所有单个结果都满足条数要求: %v\n", allValid)

	if len(invalidResults) > 0 {
		fmt.Printf("不满足条件的结果数量: %d\n", len(invalidResults))
		fmt.Printf("前10个不满足条件的结果:\n")
		for i, invalid := range invalidResults {
			if i >= 10 {
				fmt.Printf("... 还有 %d 个结果不满足条件\n", len(invalidResults)-10)
				break
			}
			fmt.Printf("  - %s\n", invalid)
		}
	}

	if allValid {
		fmt.Printf("\n✅ 所有条件都满足，可以执行 write_normal_data_record_to_redis\n")
	} else {
		fmt.Printf("\n❌ 数据不满足要求，需要继续生成数据\n")
	}
}
