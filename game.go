package main

import (
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"os"
	"github.com/go-redis/redis"
)

const (
	MAX_BET_CONFIG_COUNT = 15 // 下注选项数目
)

const (
	ICON_BLANK       = 0
	ICON_BAR_1       = 1
	ICON_BAR_2       = 2
	ICON_BAR_3       = 3
	ICON_7           = 4
	ICON_BONUS       = 5
	ICON_BONUS_SUPER = 6
	ICON_FREE        = 7
	ICON_WILD_2X     = 8
	ICON_WILD_3X     = 9
	ICON_WILD_5X     = 10
	ICON_WILD        = 11
)

// 单位元
var betConfigCount = [MAX_BET_CONFIG_COUNT]float32{0.5, 1, 2, 3, 5, 10, 20, 30, 40, 50, 80, 100, 200, 500, 1000}

var odd_config = [5]int{100, 500, 2100, 11100, 111100}

// odd_info 数组，用于获取不同图标的赔率
var odd_info = []int{1, 3, 5, 8, 10, 40, 135, 625}

type UserGameInfo struct {
	Bet             int // 下注数目
	Result          int // 总奖励
	PayOut          int // 实际输赢
	PlateWin        int
	Icons           []int
	AwardType       int
	Mul             int    // 倍数
	RoundResult     int    // 单轮结果
	FreeCount       int    // 总免费次数
	CurFreeRoundNum int    //当前第几次免费轮次
	FreeRoundResult []int  //单次免费轮次结果
	BaseBonus       [3]int //奖金游戏每列基础奖励
	BonusColNum     [3]int //每列bonus的次数
}

func (gameInfo *UserGameInfo) Reset() {

	gameInfo.Result = 0
	gameInfo.PayOut = 0
	gameInfo.AwardType = -1
	gameInfo.PlateWin = 0
}
func init() {
	rand.Seed(time.Now().UnixNano())
}

func RandUInt() uint32 {
	const mask = 0x00000FFF
	var result uint32
	result |= uint32(rand.Intn(mask + 1))
	result |= uint32(rand.Intn(mask+1)) << 12
	result |= uint32(rand.Intn(mask+1)) << 24
	return result
}

// abs 返回整数的绝对值
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

// getRand 返回一个在[nMin, nMax]范围内的随机整数
func getRand(nMin, nMax int) int {
	if nMin > nMax {
		nMin, nMax = nMax, nMin
	}
	nDiff := abs(nMax-nMin) + 1
	result := (int)(RandUInt()%uint32(nDiff) + uint32(nMin))
	return result
}

func get_normal_rand_icon() int {
	vec := []int{180, 1500, 1200, 1000, 800, 500, 0, 0, 200, 100, 50, 300}
	sum := 0

	for _, num := range vec {
		sum += num
	}
	r := getRand(0, sum)
	for i := 0; i < len(vec); i++ {
		if r < vec[i] {
			return i
		} else {
			r -= vec[i]
		}
	}
	return 0
}

func get_free_rand_icon() int {
	vec := []int{1800, 1500, 1200, 1000, 800, 400, 30, 50, 200, 100, 50, 300}
	sum := 0

	for _, num := range vec {
		sum += num
	}
	r := getRand(0, sum)
	for i := 0; i < len(vec); i++ {
		if r < vec[i] {
			return i
		} else {
			r -= vec[i]
		}
	}
	return 0
}

func RandFillIcons(icon *[3][3]int) {
	iconCount := [ICON_WILD + 1]int{0} //统计图标个数
	for i := 0; i < 3; i++ {
		for j := 0; j < 3; j++ {
			var id = get_normal_rand_icon()
			//每一列只能有一个bonus
			if id == ICON_BONUS {
				if icon[i][0] == ICON_BONUS || icon[i][1] == ICON_BONUS || icon[i][2] == ICON_BONUS {
					for {
						id = get_free_rand_icon()
						if id != ICON_BONUS {
							break
						}
					}
				}
			}
			//中间行不能出现11
			if id == ICON_WILD && j == 1 {
				for {
					id = get_free_rand_icon()
					if id != ICON_WILD {
						break
					}
				}
			}
			iconCount[id]++
			(*icon)[i][j] = id
		}

	}

}

func RandFillFreeIcons(icon *[3][3]int) {
	iconCount := [ICON_WILD + 1]int{0} //统计图标个数

	for i := 0; i < 3; i++ {
		for j := 0; j < 3; j++ {
			var id = get_free_rand_icon()
			if id == ICON_FREE {
				if iconCount[ICON_FREE]+1 > 3 {
					for {
						id = get_free_rand_icon()
						if id != ICON_FREE {
							break
						}
					}
				}
			}

			//只能出现一个super
			if id == ICON_BONUS_SUPER && iconCount[ICON_BONUS_SUPER] > 0 {
				for {
					id = get_free_rand_icon()
					if id != ICON_BONUS_SUPER {
						break
					}
				}
			}
			//每一列只能有一个bonus
			if id == ICON_BONUS {
				if icon[i][0] == ICON_BONUS || icon[i][1] == ICON_BONUS || icon[i][2] == ICON_BONUS {
					for {
						id = get_free_rand_icon()
						if id != ICON_BONUS {
							break
						}
					}
				}
			}
			//中间行不能出现8-11
			if id >= ICON_WILD_2X && id <= ICON_WILD && j == 1 {
				for {
					var nId = get_free_rand_icon()
					if id != nId {
						id = nId
						break
					}
				}
			}
			iconCount[id]++
			(*icon)[i][j] = id
		}

	}
}

func getOdds(iconCount [ICON_WILD + 1]int, nWildCount int) int {
	if iconCount[ICON_BAR_1]+nWildCount == 3 {
		return odd_info[1]
	} else if iconCount[ICON_BAR_2]+nWildCount == 3 {
		return odd_info[2]
	} else if iconCount[ICON_BAR_3]+nWildCount == 3 {
		return odd_info[3]
	} else if iconCount[ICON_7]+nWildCount == 3 {
		return odd_info[4]
	} else if iconCount[ICON_BONUS] == 3 {
		return 3
	} else if iconCount[ICON_BONUS] == 2 {
		return 2
	}

	return 1
}

func getMul(count int, num int) int {
	var nMul = 1
	for i := 0; i < count; i++ {
		nMul *= num
	}
	return nMul
}

func CalcResultTimes(gameInfo *UserGameInfo, icons *[3][3]int, mainGame bool) int {
	gameInfo.Bet = 1                                                    //投注
	iconCount := [ICON_WILD + 1]int{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} //统计图标个数
	freeCount := 0                                                      //free图标个数
	bonusCount := 0                                                     //bonus图标个数
	superBonusCount := 0                                                //super bonus图标个数
	bonusCol := [3]int{0, 0, 0}                                         //bonus每列有的个数
	for i := 0; i < 3; i++ {
		for j := 0; j < 3; j++ {
			if icons[i][j] == ICON_BONUS {
				bonusCount++
				bonusCol[i]++
			}
			if !mainGame {
				if icons[i][j] == ICON_BONUS_SUPER {
					superBonusCount++
				}
				if icons[i][j] == ICON_FREE {
					freeCount++
				}
			}
		}
	}

	iconCount[icons[0][1]]++
	iconCount[icons[1][1]]++
	iconCount[icons[2][1]]++

	nMul := getMul(iconCount[ICON_WILD], 2) * getMul(iconCount[ICON_WILD_2X], 2) * getMul(iconCount[ICON_WILD_3X], 3) * getMul(iconCount[ICON_WILD_5X], 5)
	nWildCount := iconCount[ICON_WILD_2X] + iconCount[ICON_WILD_3X] + iconCount[ICON_WILD_5X]

	if nMul == 0 {
		nMul = 1
	}
	gameInfo.Mul = nMul
	// log.Println("中间行图标 ", icons[0][1], icons[1][1], icons[2][1], " 倍数:", nMul, "是否主游戏:", mainGame, "bonusCount", bonusCount)

	odds := 0
	if nWildCount == 3 {
		odds = 5
	} else {
		if iconCount[ICON_BLANK] == 0 && iconCount[ICON_WILD] == 0 && iconCount[ICON_FREE] == 0 {
			odds = getOdds(iconCount, nWildCount)
		}
	}

	var bonus = 0
	var roundResult = 0
	if mainGame {
		if bonusCount >= 3 && bonusCol[0] == 1 && bonusCol[1] == 1 && bonusCol[2] == 1 {
			gameInfo.FreeCount = 10
			gameInfo.BaseBonus[0] = 2 * gameInfo.Bet
			gameInfo.BaseBonus[1] = 2 * gameInfo.Bet
			gameInfo.BaseBonus[2] = 2 * gameInfo.Bet
			bonus = 3 * gameInfo.Bet

			gameInfo.BonusColNum[0] = 1
			gameInfo.BonusColNum[1] = 1
			gameInfo.BonusColNum[2] = 1
		}
		roundResult = odds*nMul + bonus
	} else {
		if freeCount == 2 {
			gameInfo.FreeCount += 5
		} else if freeCount == 3 {
			gameInfo.FreeCount += 10
		}
		var addBonus = 0
		if bonusCol[0] == 1 {
			var winBonus = gameInfo.BaseBonus[0]
			gameInfo.BaseBonus[0] = gameInfo.BaseBonus[0] + gameInfo.BonusColNum[0]*gameInfo.Bet
			gameInfo.BonusColNum[0]++

			addBonus += winBonus
		}
		if bonusCol[1] == 1 {
			var winBonus = gameInfo.BaseBonus[1]
			gameInfo.BaseBonus[1] = gameInfo.BaseBonus[1] + gameInfo.BonusColNum[1]*gameInfo.Bet
			gameInfo.BonusColNum[1]++

			addBonus += winBonus
		}
		if bonusCol[2] == 1 {
			var winBonus = gameInfo.BaseBonus[2]
			gameInfo.BaseBonus[2] = gameInfo.BaseBonus[2] + gameInfo.BonusColNum[2]*gameInfo.Bet
			gameInfo.BonusColNum[2]++

			addBonus += winBonus
		}

		if superBonusCount > 0 {
			var winBonus = gameInfo.BaseBonus[0]
			gameInfo.BaseBonus[0] = gameInfo.BaseBonus[0] + gameInfo.BonusColNum[0]*gameInfo.Bet
			gameInfo.BonusColNum[0]++
			addBonus += winBonus

			winBonus = gameInfo.BaseBonus[1]
			gameInfo.BaseBonus[1] = gameInfo.BaseBonus[1] + gameInfo.BonusColNum[1]*gameInfo.Bet
			gameInfo.BonusColNum[1]++
			addBonus += winBonus

			winBonus = gameInfo.BaseBonus[2]
			gameInfo.BaseBonus[2] = gameInfo.BaseBonus[2] + gameInfo.BonusColNum[2]*gameInfo.Bet
			gameInfo.BonusColNum[2]++
			addBonus += winBonus
		}

		bonus = addBonus
		if bonusCount > 0 || superBonusCount > 0 {
			odds = 0
		}

		if odds > 0 {
			roundResult = odds * nMul
		} else {
			roundResult = bonus
		}
	}

	gameInfo.RoundResult = roundResult
	gameInfo.Result += gameInfo.RoundResult
	log.Println("当前轮结果 ", gameInfo.RoundResult, " 累加结果:", gameInfo.Result)
	return gameInfo.Result
}

func write_normal_data_record_to_redis(mMapResult map[int][]string) {
	//写redis
	rdb := redis.NewClient(&redis.Options{
		Addr:     "47.236.236.24:6379",             // Redis 服务器地址
		Password: "E8LmKBGgoVm00KYeVDao03NRkPXmF8", // Redis 服务器密码（如果没有密码，可以留空）
		DB:       3,                                // 使用默认数据库
	})
	defer rdb.Close()
	//普通玩法记录写redis
	var hashKey string
	hashKey = "CBT_"
	for outerKey, stringvalue := range mMapResult {
		strKey := hashKey + strconv.Itoa(outerKey)

		tempMap := make(map[int]string)

		for i, str := range stringvalue {
			parts := strings.Split(str, ",")
			intSlice := make([]int, len(parts))
			// 将每个部分转换为整数
			for j, part := range parts {
				num, err := strconv.Atoi(part)
				if err != nil {
					fmt.Printf("Error converting string '%s' to int: %v\n", part, err)
					return
				}
				intSlice[j] = num
			}
			// 将转换后的整数切片存储到二维切片中
			intMatrix := make([][]int, 1)
			intMatrix[0] = intSlice
			jsonData, _ := json.Marshal(intMatrix)
			tempMap[i] = string(jsonData)

		}
		elements := make([]interface{}, 0, len(tempMap))
		nCount := 0
		push_len := 30
		// 遍历 map，将数据转换为字符串格式并添加到切片中
		for _, value := range tempMap {

			elements = append(elements, value)
			nCount++
			if (nCount % push_len) == 0 {
				err := rdb.RPush(strKey, elements...).Err()
				if err != nil {
					//panic(err)
				}
				elements = elements[:0]
			}
		}
		err := rdb.RPush(strKey, elements...).Err()
		if err != nil {
			//panic(err)
		}
	}
}

func appendIcons(gameInfo *UserGameInfo, szICon *[3][3]int) {
	for i := 0; i < 3; i++ {
		for j := 0; j < 3; j++ {
			gameInfo.Icons = append(gameInfo.Icons, szICon[i][j])
		}
	}
}

func readJsonData() map[int]int {
	// 读取JSON文件
	data, err := os.ReadFile("stock_ctr_32_5045_118.json")
	if err != nil {
		fmt.Println("Error reading file:", err)
		return nil
	}

	// 解析JSON数据
	var result map[string]interface{}
	if err := json.Unmarshal(data, &result); err != nil {
		fmt.Println("Error parsing JSON:", err)
		return nil
	}

	// 提取bet_normal_win数组
	betNormalWin, exists := result["bet_normal_win"].([]interface{})
	if !exists {
		fmt.Println("bet_normal_win field not found")
		return nil
	}

	// 创建map存储结果
	multerRateMap := make(map[int]int)

	// 遍历bet_normal_win数组
	for _, item := range betNormalWin {
		// 将每个元素转换为map
		itemMap, ok := item.(map[string]interface{})
		if !ok {
			fmt.Println("Invalid item in bet_normal_win")
			continue
		}

		// 提取multer和rate字段
		multer, multerExists := itemMap["multer"].(float64)
		rate, rateExists := itemMap["rate"].(float64)

		if !multerExists || !rateExists {
			fmt.Println("Missing multer or rate field in item")
			continue
		}

		// 将float64转换为int并存储到map中
		multerRateMap[int(multer)] = int(rate)
	}

	// 打印结果
	for multer, rate := range multerRateMap {
		fmt.Printf("Multer: %d, Rate: %d\n", multer, rate)
	}
	return multerRateMap
}

func main() {
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)

	mMapResult := make(map[int][]string)
	mMapStr := make(map[string]int)
	//mMapStr := make(map[int]map[string]int)

	szICon := [3][3]int{{0, 0, 0}, {0, 0, 0}, {0, 0, 0}}

	//1-20 200个 20-50 100个  50以上50个
	var b1 = false
	var b2 = false
	var b3 = false

	multerRateMap := readJsonData()
	log.Println("开始跑数据")
	for {

		RandFillIcons(&szICon)
		var gameInfo UserGameInfo
		gameInfo.Bet = 1
		gameInfo.Result = 0

		// szICon[0][1] = ICON_BONUS
		// szICon[1][1] = ICON_BONUS
		// szICon[2][1] = ICON_BONUS

		CalcResultTimes(&gameInfo, &szICon, true)
		appendIcons(&gameInfo, &szICon)
		
		if gameInfo.FreeCount > 0 {
			for i := 0; i < gameInfo.FreeCount; i++ {
				RandFillFreeIcons(&szICon)
				appendIcons(&gameInfo, &szICon)
				CalcResultTimes(&gameInfo, &szICon, false)
				gameInfo.CurFreeRoundNum++
				// log.Println("结果 ", gameInfo.Result, "当前免费次数 ",gameInfo.CurFreeRoundNum)
				if gameInfo.CurFreeRoundNum >= gameInfo.FreeCount {
					break
				}
			}
		}
		if gameInfo.Result == 0 {
			continue
		}
		log.Println("结果 ", gameInfo.Result, "总免费次数 ", gameInfo.FreeCount)
		if multerRateMap != nil {
			if _, exists := multerRateMap[gameInfo.Result]; !exists {
				continue
			}
		}
		var strResultBuilder strings.Builder
		strResultBuilder.Reset()

		for y := 0; y < len(gameInfo.Icons); y++ {
			strResultBuilder.WriteString(fmt.Sprintf("%d,", gameInfo.Icons[y]))
		}
		strResult := strResultBuilder.String()
		if len(strResult) == 0 {
			continue
		}
		strResult = strResult[:len(strResult)-1]
		if _, exists := mMapStr[strResult]; exists {
			continue
		}
		mMapStr[strResult] = 1
		log.Println("结果 ", gameInfo.Result, "图标个数 ", len(gameInfo.Icons))
		if gameInfo.Result > 0 {
			if gameInfo.Result <= 20 {
				if len(mMapResult[gameInfo.Result]) <= 200 {
					mMapResult[gameInfo.Result] = append(mMapResult[gameInfo.Result], strResult)
				} else {
					b1 = true
				}
			} else if gameInfo.Result > 20 && gameInfo.Result <= 50 {
				if len(mMapResult[gameInfo.Result]) <= 100 {
					mMapResult[gameInfo.Result] = append(mMapResult[gameInfo.Result], strResult)
				} else {
					b2 = true
				}
			} else {
				if len(mMapResult[gameInfo.Result]) <= 50 {
					mMapResult[gameInfo.Result] = append(mMapResult[gameInfo.Result], strResult)
				} else {
					b3 = true
				}
			}

		}
		b1 = true
		b2 = true
		b3 = true
		if b1 && b2 && b3 {
			break
		}
	}
	log.Println("完成", b1, b2, b3)
	log.Println("数据 ", len(mMapResult))
	//write_normal_data_record_to_redis(mMapResult)

}
