package main

import (
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"os"

	"github.com/go-redis/redis"
)

const (
	MAX_BET_CONFIG_COUNT = 15 // 下注选项数目
)

const (
	ICON_BLANK       = 0
	ICON_BAR_1       = 1
	ICON_BAR_2       = 2
	ICON_BAR_3       = 3
	ICON_7           = 4
	ICON_BONUS       = 5
	ICON_BONUS_SUPER = 6
	ICON_FREE        = 7
	ICON_WILD_2X     = 8
	ICON_WILD_3X     = 9
	ICON_WILD_5X     = 10
	ICON_WILD        = 11
)

// 单位元
var betConfigCount = [MAX_BET_CONFIG_COUNT]float32{0.5, 1, 2, 3, 5, 10, 20, 30, 40, 50, 80, 100, 200, 500, 1000}

var odd_config = [5]int{100, 500, 2100, 11100, 111100}

// odd_info 数组，用于获取不同图标的赔率
var odd_info = []int{1, 3, 5, 8, 10, 40, 135, 625}

type UserGameInfo struct {
	Bet             int // 下注数目
	Result          int // 总奖励
	PayOut          int // 实际输赢
	PlateWin        int
	Icons           []int
	AwardType       int
	Mul             int    // 倍数
	RoundResult     int    // 单轮结果
	FreeCount       int    // 总免费次数
	CurFreeRoundNum int    //当前第几次免费轮次
	FreeRoundResult []int  //单次免费轮次结果
	BaseBonus       [3]int //奖金游戏每列基础奖励
	BonusColNum     [3]int //每列bonus的次数
}

func (gameInfo *UserGameInfo) Reset() {

	gameInfo.Result = 0
	gameInfo.PayOut = 0
	gameInfo.AwardType = -1
	gameInfo.PlateWin = 0
}
func init() {
	rand.Seed(time.Now().UnixNano())
}

func RandUInt() uint32 {
	const mask = 0x00000FFF
	var result uint32
	result |= uint32(rand.Intn(mask + 1))
	result |= uint32(rand.Intn(mask+1)) << 12
	result |= uint32(rand.Intn(mask+1)) << 24
	return result
}

// abs 返回整数的绝对值
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

// getRand 返回一个在[nMin, nMax]范围内的随机整数
func getRand(nMin, nMax int) int {
	if nMin > nMax {
		nMin, nMax = nMax, nMin
	}
	nDiff := abs(nMax-nMin) + 1
	result := (int)(RandUInt()%uint32(nDiff) + uint32(nMin))
	return result
}

func get_normal_rand_icon() int {
	vec := []int{180, 1500, 1200, 1000, 800, 500, 0, 0, 200, 100, 50, 300}
	sum := 0

	for _, num := range vec {
		sum += num
	}
	r := getRand(0, sum)
	for i := 0; i < len(vec); i++ {
		if r < vec[i] {
			return i
		} else {
			r -= vec[i]
		}
	}
	return 0
}

func get_free_rand_icon() int {
	vec := []int{1800, 1500, 1200, 1000, 800, 400, 30, 50, 200, 100, 50, 300}
	sum := 0

	for _, num := range vec {
		sum += num
	}
	r := getRand(0, sum)
	for i := 0; i < len(vec); i++ {
		if r < vec[i] {
			return i
		} else {
			r -= vec[i]
		}
	}
	return 0
}

func RandFillIcons(icon *[3][3]int) {
	iconCount := [ICON_WILD + 1]int{0} //统计图标个数
	for i := 0; i < 3; i++ {
		for j := 0; j < 3; j++ {
			var id = get_normal_rand_icon()
			//每一列只能有一个bonus
			if id == ICON_BONUS {
				if icon[i][0] == ICON_BONUS || icon[i][1] == ICON_BONUS || icon[i][2] == ICON_BONUS {
					for {
						id = get_free_rand_icon()
						if id != ICON_BONUS {
							break
						}
					}
				}
			}
			//中间行不能出现11
			if id == ICON_WILD && j == 1 {
				for {
					id = get_free_rand_icon()
					if id != ICON_WILD {
						break
					}
				}
			}
			iconCount[id]++
			(*icon)[i][j] = id
		}

	}

}

func RandFillFreeIcons(icon *[3][3]int) {
	iconCount := [ICON_WILD + 1]int{0} //统计图标个数

	for i := 0; i < 3; i++ {
		for j := 0; j < 3; j++ {
			var id = get_free_rand_icon()
			if id == ICON_FREE {
				if iconCount[ICON_FREE]+1 > 3 {
					for {
						id = get_free_rand_icon()
						if id != ICON_FREE {
							break
						}
					}
				}
			}

			//只能出现一个super
			if id == ICON_BONUS_SUPER && iconCount[ICON_BONUS_SUPER] > 0 {
				for {
					id = get_free_rand_icon()
					if id != ICON_BONUS_SUPER {
						break
					}
				}
			}
			//每一列只能有一个bonus
			if id == ICON_BONUS {
				if icon[i][0] == ICON_BONUS || icon[i][1] == ICON_BONUS || icon[i][2] == ICON_BONUS {
					for {
						id = get_free_rand_icon()
						if id != ICON_BONUS {
							break
						}
					}
				}
			}
			//中间行不能出现8-11
			if id >= ICON_WILD_2X && id <= ICON_WILD && j == 1 {
				for {
					var nId = get_free_rand_icon()
					if id != nId {
						id = nId
						break
					}
				}
			}
			iconCount[id]++
			(*icon)[i][j] = id
		}

	}
}

func getOdds(iconCount [ICON_WILD + 1]int, nWildCount int) int {
	if iconCount[ICON_BAR_1]+nWildCount == 3 {
		return odd_info[1]
	} else if iconCount[ICON_BAR_2]+nWildCount == 3 {
		return odd_info[2]
	} else if iconCount[ICON_BAR_3]+nWildCount == 3 {
		return odd_info[3]
	} else if iconCount[ICON_7]+nWildCount == 3 {
		return odd_info[4]
	} else if iconCount[ICON_BONUS] == 3 {
		return 3
	} else if iconCount[ICON_BONUS] == 2 {
		return 2
	}

	return 1
}

func getMul(count int, num int) int {
	var nMul = 1
	for i := 0; i < count; i++ {
		nMul *= num
	}
	return nMul
}

func CalcResultTimes(gameInfo *UserGameInfo, icons *[3][3]int, mainGame bool) int {
	gameInfo.Bet = 1                                                    //投注
	iconCount := [ICON_WILD + 1]int{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} //统计图标个数
	freeCount := 0                                                      //free图标个数
	bonusCount := 0                                                     //bonus图标个数
	superBonusCount := 0                                                //super bonus图标个数
	bonusCol := [3]int{0, 0, 0}                                         //bonus每列有的个数
	for i := 0; i < 3; i++ {
		for j := 0; j < 3; j++ {
			if icons[i][j] == ICON_BONUS {
				bonusCount++
				bonusCol[i]++
			}
			if !mainGame {
				if icons[i][j] == ICON_BONUS_SUPER {
					superBonusCount++
				}
				if icons[i][j] == ICON_FREE {
					freeCount++
				}
			}
		}
	}

	iconCount[icons[0][1]]++
	iconCount[icons[1][1]]++
	iconCount[icons[2][1]]++

	nMul := getMul(iconCount[ICON_WILD], 2) * getMul(iconCount[ICON_WILD_2X], 2) * getMul(iconCount[ICON_WILD_3X], 3) * getMul(iconCount[ICON_WILD_5X], 5)
	nWildCount := iconCount[ICON_WILD_2X] + iconCount[ICON_WILD_3X] + iconCount[ICON_WILD_5X]

	if nMul == 0 {
		nMul = 1
	}
	gameInfo.Mul = nMul
	// log.Println("中间行图标 ", icons[0][1], icons[1][1], icons[2][1], " 倍数:", nMul, "是否主游戏:", mainGame, "bonusCount", bonusCount)

	odds := 0
	if nWildCount == 3 {
		odds = 5
	} else {
		if iconCount[ICON_BLANK] == 0 && iconCount[ICON_WILD] == 0 && iconCount[ICON_FREE] == 0 {
			odds = getOdds(iconCount, nWildCount)
		}
	}

	var bonus = 0
	var roundResult = 0
	if mainGame {
		if bonusCount >= 3 && bonusCol[0] == 1 && bonusCol[1] == 1 && bonusCol[2] == 1 {
			gameInfo.FreeCount = 10
			gameInfo.BaseBonus[0] = 2 * gameInfo.Bet
			gameInfo.BaseBonus[1] = 2 * gameInfo.Bet
			gameInfo.BaseBonus[2] = 2 * gameInfo.Bet
			bonus = 3 * gameInfo.Bet

			gameInfo.BonusColNum[0] = 1
			gameInfo.BonusColNum[1] = 1
			gameInfo.BonusColNum[2] = 1
		}
		roundResult = odds*nMul + bonus
	} else {
		if freeCount == 2 {
			gameInfo.FreeCount += 5
		} else if freeCount == 3 {
			gameInfo.FreeCount += 10
		}
		var addBonus = 0
		if bonusCol[0] == 1 {
			var winBonus = gameInfo.BaseBonus[0]
			gameInfo.BaseBonus[0] = gameInfo.BaseBonus[0] + gameInfo.BonusColNum[0]*gameInfo.Bet
			gameInfo.BonusColNum[0]++

			addBonus += winBonus
		}
		if bonusCol[1] == 1 {
			var winBonus = gameInfo.BaseBonus[1]
			gameInfo.BaseBonus[1] = gameInfo.BaseBonus[1] + gameInfo.BonusColNum[1]*gameInfo.Bet
			gameInfo.BonusColNum[1]++

			addBonus += winBonus
		}
		if bonusCol[2] == 1 {
			var winBonus = gameInfo.BaseBonus[2]
			gameInfo.BaseBonus[2] = gameInfo.BaseBonus[2] + gameInfo.BonusColNum[2]*gameInfo.Bet
			gameInfo.BonusColNum[2]++

			addBonus += winBonus
		}

		if superBonusCount > 0 {
			var winBonus = gameInfo.BaseBonus[0]
			gameInfo.BaseBonus[0] = gameInfo.BaseBonus[0] + gameInfo.BonusColNum[0]*gameInfo.Bet
			gameInfo.BonusColNum[0]++
			addBonus += winBonus

			winBonus = gameInfo.BaseBonus[1]
			gameInfo.BaseBonus[1] = gameInfo.BaseBonus[1] + gameInfo.BonusColNum[1]*gameInfo.Bet
			gameInfo.BonusColNum[1]++
			addBonus += winBonus

			winBonus = gameInfo.BaseBonus[2]
			gameInfo.BaseBonus[2] = gameInfo.BaseBonus[2] + gameInfo.BonusColNum[2]*gameInfo.Bet
			gameInfo.BonusColNum[2]++
			addBonus += winBonus
		}

		bonus = addBonus
		if bonusCount > 0 || superBonusCount > 0 {
			odds = 0
		}

		if odds > 0 {
			roundResult = odds * nMul
		} else {
			roundResult = bonus
		}
	}

	gameInfo.RoundResult = roundResult
	gameInfo.Result += gameInfo.RoundResult
	// log.Println("当前轮结果 ", gameInfo.RoundResult, " 累加结果:", gameInfo.Result)
	return gameInfo.Result
}

func write_normal_data_record_to_redis(mMapResult map[int][]string) {
	//写redis
	rdb := redis.NewClient(&redis.Options{
		Addr:     "47.236.236.24:6379",             // Redis 服务器地址
		Password: "E8LmKBGgoVm00KYeVDao03NRkPXmF8", // Redis 服务器密码（如果没有密码，可以留空）
		DB:       3,                                // 使用默认数据库
	})
	defer rdb.Close()
	//普通玩法记录写redis
	var hashKey string
	hashKey = "CBT_"
	for outerKey, stringvalue := range mMapResult {
		strKey := hashKey + strconv.Itoa(outerKey)

		tempMap := make(map[int]string)

		for i, str := range stringvalue {
			parts := strings.Split(str, ",")
			intSlice := make([]int, len(parts))
			// 将每个部分转换为整数
			for j, part := range parts {
				num, err := strconv.Atoi(part)
				if err != nil {
					fmt.Printf("Error converting string '%s' to int: %v\n", part, err)
					return
				}
				intSlice[j] = num
			}
			// 将转换后的整数切片存储到二维切片中
			intMatrix := make([][]int, 1)
			intMatrix[0] = intSlice
			jsonData, _ := json.Marshal(intMatrix)
			tempMap[i] = string(jsonData)

		}
		elements := make([]interface{}, 0, len(tempMap))
		nCount := 0
		push_len := 30
		// 遍历 map，将数据转换为字符串格式并添加到切片中
		for _, value := range tempMap {

			elements = append(elements, value)
			nCount++
			if (nCount % push_len) == 0 {
				err := rdb.RPush(strKey, elements...).Err()
				if err != nil {
					//panic(err)
				}
				elements = elements[:0]
			}
		}
		err := rdb.RPush(strKey, elements...).Err()
		if err != nil {
			//panic(err)
		}
	}
}

func appendIcons(gameInfo *UserGameInfo, szICon *[3][3]int) {
	for i := 0; i < 3; i++ {
		for j := 0; j < 3; j++ {
			gameInfo.Icons = append(gameInfo.Icons, szICon[i][j])
		}
	}
}

// saveMapResultToJSON 将 mMapResult 保存为 JSON 文件
func saveMapResultToJSON(mMapResult map[int][]string, filename string) error {
	// 将 map 转换为 JSON 格式
	jsonData, err := json.MarshalIndent(mMapResult, "", "  ")
	if err != nil {
		return fmt.Errorf("JSON 序列化失败: %v", err)
	}

	// 写入文件
	err = os.WriteFile(filename, jsonData, 0644)
	if err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}

	fmt.Printf("数据已成功保存到文件: %s\n", filename)
	return nil
}

func readJsonData() map[int]int {
	// 读取JSON文件
	data, err := os.ReadFile("stock_ctr_32_5045_118.json")
	if err != nil {
		fmt.Println("Error reading file:", err)
		return nil
	}

	// 解析JSON数据
	var result map[string]interface{}
	if err := json.Unmarshal(data, &result); err != nil {
		fmt.Println("Error parsing JSON:", err)
		return nil
	}

	// 提取bet_normal_win数组
	betNormalWin, exists := result["bet_normal_win"].([]interface{})
	if !exists {
		fmt.Println("bet_normal_win field not found")
		return nil
	}

	// 创建map存储结果
	multerRateMap := make(map[int]int)

	// 遍历bet_normal_win数组
	for _, item := range betNormalWin {
		// 将每个元素转换为map
		itemMap, ok := item.(map[string]interface{})
		if !ok {
			fmt.Println("Invalid item in bet_normal_win")
			continue
		}

		// 提取multer和rate字段（它们在JSON中是字符串类型）
		multerStr, multerExists := itemMap["multer"].(string)
		rateStr, rateExists := itemMap["rate"].(string)

		if !multerExists || !rateExists {
			fmt.Println("Missing multer or rate field in item")
			continue
		}

		// 将字符串转换为int
		multer, err := strconv.Atoi(multerStr)
		if err != nil {
			fmt.Printf("Error converting multer '%s' to int: %v\n", multerStr, err)
			continue
		}

		rate, err := strconv.Atoi(rateStr)
		if err != nil {
			fmt.Printf("Error converting rate '%s' to int: %v\n", rateStr, err)
			continue
		}

		// 存储到map中
		multerRateMap[multer] = rate
	}

	// 打印结果 - 按multer从小到大排序
	// var multers []int
	// for multer := range multerRateMap {
	// 	multers = append(multers, multer)
	// }
	// sort.Ints(multers)

	// for _, multer := range multers {
	// 	rate := multerRateMap[multer]
	// 	fmt.Printf("Multer: %d, Rate: %d\n", multer, rate)
	// }
	return multerRateMap
}

// analyzeResultRange 分析结果范围，帮助理解40645的可能性
func analyzeResultRange() {
	log.Println("分析游戏结果范围...")

	maxResult := 0
	minResult := 999999
	resultCount := make(map[int]int)
	maxAttempts := 100000

	for attempt := 0; attempt < maxAttempts; attempt++ {
		szICon := [3][3]int{{0, 0, 0}, {0, 0, 0}, {0, 0, 0}}

		// 随机填充图标
		RandFillIcons(&szICon)

		var gameInfo UserGameInfo
		gameInfo.Bet = 1
		gameInfo.Result = 0

		// 计算主游戏结果
		CalcResultTimes(&gameInfo, &szICon, true)
		appendIcons(&gameInfo, &szICon)

		// 如果有免费游戏，继续计算
		if gameInfo.FreeCount > 0 {
			for i := 0; i < gameInfo.FreeCount; i++ {
				RandFillFreeIcons(&szICon)
				appendIcons(&gameInfo, &szICon)
				CalcResultTimes(&gameInfo, &szICon, false)
				gameInfo.CurFreeRoundNum++
				if gameInfo.CurFreeRoundNum >= gameInfo.FreeCount {
					break
				}
			}
		}

		if gameInfo.Result > 0 {
			if gameInfo.Result > maxResult {
				maxResult = gameInfo.Result
			}
			if gameInfo.Result < minResult {
				minResult = gameInfo.Result
			}
			resultCount[gameInfo.Result]++
		}
	}

	log.Printf("分析完成！最小结果: %d, 最大结果: %d", minResult, maxResult)
	log.Printf("40645是否在可能范围内: %t", maxResult >= 40645)

	// 打印一些高结果的例子
	log.Println("高结果统计:")
	for result, count := range resultCount {
		if result > 10000 {
			log.Printf("结果 %d: 出现 %d 次", result, count)
		}
	}
}

// findIconsFor40645 专门寻找能产生40645结果的图标组合
func findIconsFor40645() {
	log.Println("开始寻找能产生40645结果的图标组合...")

	// 先分析结果范围
	analyzeResultRange()

	targetResult := 40645
	foundCount := 0
	maxAttempts := 5000000 // 增加尝试次数

	for attempt := 0; attempt < maxAttempts; attempt++ {
		szICon := [3][3]int{{0, 0, 0}, {0, 0, 0}, {0, 0, 0}}

		// 随机填充图标
		RandFillIcons(&szICon)

		var gameInfo UserGameInfo
		gameInfo.Bet = 1
		gameInfo.Result = 0

		// 计算主游戏结果
		CalcResultTimes(&gameInfo, &szICon, true)
		appendIcons(&gameInfo, &szICon)

		// 如果有免费游戏，继续计算
		if gameInfo.FreeCount > 0 {
			for i := 0; i < gameInfo.FreeCount; i++ {
				RandFillFreeIcons(&szICon)
				appendIcons(&gameInfo, &szICon)
				CalcResultTimes(&gameInfo, &szICon, false)
				gameInfo.CurFreeRoundNum++
				if gameInfo.CurFreeRoundNum >= gameInfo.FreeCount {
					break
				}
			}
		}

		// 检查是否达到目标结果或接近目标结果
		if gameInfo.Result == targetResult {
			foundCount++
			log.Printf("找到第%d个40645结果组合！", foundCount)

			// 打印图标数据
			fmt.Printf("图标数据: ")
			for i, icon := range gameInfo.Icons {
				if i > 0 {
					fmt.Printf(",")
				}
				fmt.Printf("%d", icon)
			}
			fmt.Printf("\n")

			// 打印详细信息
			fmt.Printf("总免费次数: %d, 最终结果: %d\n", gameInfo.FreeCount, gameInfo.Result)
			fmt.Printf("图标总数: %d\n", len(gameInfo.Icons))

			// 找到1个就停止
			if foundCount >= 1 {
				break
			}
		} else if gameInfo.Result > 35000 {
			// 打印接近目标的高结果
			log.Printf("发现高结果: %d (免费次数: %d)", gameInfo.Result, gameInfo.FreeCount)
		}

		// 每50万次尝试打印进度
		if attempt > 0 && attempt%500000 == 0 {
			log.Printf("已尝试 %d 次，找到 %d 个40645结果", attempt, foundCount)
		}
	}

	if foundCount == 0 {
		log.Printf("在%d次尝试中未找到40645结果的组合", maxAttempts)
	} else {
		log.Printf("总共找到 %d 个40645结果的组合", foundCount)
	}
}

// constructIconsFor40645 通过逆向工程构造能产生40645结果的图标组合
func constructIconsFor40645() {
	log.Println("通过逆向工程构造40645结果...")

	targetResult := 40645

	// 分析：40645 = odds * nMul + bonus (主游戏) + 免费游戏累积
	// 最大单轮结果约为 625 * 125 = 78125 (最高赔率 * 最高倍数)
	// 所以需要通过免费游戏累积多轮结果

	// 尝试构造一个能触发免费游戏的主游戏
	szICon := [3][3]int{
		{ICON_BONUS, ICON_7, ICON_BONUS}, // 第一列有bonus
		{ICON_BONUS, ICON_7, ICON_7},     // 第二列有bonus
		{ICON_BONUS, ICON_7, ICON_7},     // 第三列有bonus
	}

	var gameInfo UserGameInfo
	gameInfo.Bet = 1
	gameInfo.Result = 0

	log.Printf("测试构造的主游戏图标组合:")
	for i := 0; i < 3; i++ {
		for j := 0; j < 3; j++ {
			fmt.Printf("%d ", szICon[i][j])
		}
		fmt.Printf("\n")
	}

	// 计算主游戏结果
	CalcResultTimes(&gameInfo, &szICon, true)
	appendIcons(&gameInfo, &szICon)

	log.Printf("主游戏结果: %d, 免费次数: %d", gameInfo.Result, gameInfo.FreeCount)

	if gameInfo.FreeCount > 0 {
		log.Printf("开始免费游戏，需要累积到 %d", targetResult)

		// 在免费游戏中尝试构造高倍数组合
		for i := 0; i < gameInfo.FreeCount && gameInfo.Result < targetResult; i++ {
			// 构造一个高倍数的免费游戏组合
			freeIcon := [3][3]int{
				{ICON_7, ICON_WILD_5X, ICON_7}, // 高倍数wild
				{ICON_7, ICON_WILD_5X, ICON_7}, // 高倍数wild
				{ICON_7, ICON_WILD_5X, ICON_7}, // 高倍数wild
			}

			appendIcons(&gameInfo, &freeIcon)
			CalcResultTimes(&gameInfo, &freeIcon, false)
			gameInfo.CurFreeRoundNum++

			log.Printf("免费游戏第%d轮，当前累积结果: %d", i+1, gameInfo.Result)

			if gameInfo.CurFreeRoundNum >= gameInfo.FreeCount {
				break
			}
		}
	}

	log.Printf("最终结果: %d (目标: %d)", gameInfo.Result, targetResult)

	if gameInfo.Result == targetResult {
		log.Printf("成功构造出40645结果！")
		// 打印完整的图标数据
		fmt.Printf("完整图标数据: ")
		for i, icon := range gameInfo.Icons {
			if i > 0 {
				fmt.Printf(",")
			}
			fmt.Printf("%d", icon)
		}
		fmt.Printf("\n")
	} else {
		log.Printf("构造失败，实际结果: %d", gameInfo.Result)

		// 尝试手动调整到目标值
		if gameInfo.Result < targetResult {
			log.Printf("结果偏小，需要增加 %d", targetResult-gameInfo.Result)
		} else {
			log.Printf("结果偏大，需要减少 %d", gameInfo.Result-targetResult)
		}
	}
}

// testSimpleCalculation 测试简单的计算
func testSimpleCalculation() {
	log.Println("测试简单计算...")

	// 测试一个简单的图标组合
	szICon := [3][3]int{
		{ICON_7, ICON_7, ICON_7},
		{ICON_7, ICON_7, ICON_7},
		{ICON_7, ICON_7, ICON_7},
	}

	var gameInfo UserGameInfo
	gameInfo.Bet = 1
	gameInfo.Result = 0

	log.Printf("测试图标组合 (全是7):")
	for i := 0; i < 3; i++ {
		for j := 0; j < 3; j++ {
			fmt.Printf("%d ", szICon[i][j])
		}
		fmt.Printf("\n")
	}

	// 计算结果
	result := CalcResultTimes(&gameInfo, &szICon, true)
	log.Printf("计算结果: %d", result)

	// 测试最高倍数组合
	szICon2 := [3][3]int{
		{ICON_7, ICON_WILD_5X, ICON_7},
		{ICON_7, ICON_WILD_5X, ICON_7},
		{ICON_7, ICON_WILD_5X, ICON_7},
	}

	var gameInfo2 UserGameInfo
	gameInfo2.Bet = 1
	gameInfo2.Result = 0

	log.Printf("测试最高倍数组合:")
	result2 := CalcResultTimes(&gameInfo2, &szICon2, true)
	log.Printf("最高倍数结果: %d", result2)
}

func main() {
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)

	log.Println("程序开始运行...")

	// 直接测试40645的数学可能性
	log.Println("分析40645的数学构成:")

	// 最高赔率是625 (ICON_7的赔率，从odd_info[4]获得)
	// 最高倍数组合：3个WILD_5X = 5^3 = 125倍
	maxSingleRound := 625 * 125
	log.Printf("单轮最大可能结果: %d", maxSingleRound)

	// 40645需要多少轮这样的结果
	roundsNeeded := 40645 / maxSingleRound
	remainder := 40645 % maxSingleRound
	log.Printf("达到40645需要: %d轮最大结果 + %d", roundsNeeded, remainder)

	// 免费游戏最多10轮，看看是否可能
	if roundsNeeded <= 10 {
		log.Printf("理论上可能通过免费游戏达到40645")
	} else {
		log.Printf("即使10轮免费游戏也无法达到40645")
	}

	return

	// 通过逆向工程构造40645结果
	constructIconsFor40645()
	return

	// 专门寻找40645结果
	findIconsFor40645()
	return

	mMapResult := make(map[int][]string)
	mMapStr := make(map[string]int)
	//mMapStr := make(map[int]map[string]int)

	szICon := [3][3]int{{0, 0, 0}, {0, 0, 0}, {0, 0, 0}}

	//1-20 200个 20-50 100个  50以上50个
	var b1 = false
	var b2 = false
	var b3 = false

	multerRateMap := readJsonData()

	log.Println("开始跑数据")
	for {

		RandFillIcons(&szICon)
		var gameInfo UserGameInfo
		gameInfo.Bet = 1
		gameInfo.Result = 0

		// szICon[0][1] = ICON_BONUS
		// szICon[1][1] = ICON_BONUS
		// szICon[2][1] = ICON_BONUS

		CalcResultTimes(&gameInfo, &szICon, true)
		appendIcons(&gameInfo, &szICon)

		if gameInfo.FreeCount > 0 {
			for i := 0; i < gameInfo.FreeCount; i++ {
				RandFillFreeIcons(&szICon)
				appendIcons(&gameInfo, &szICon)
				CalcResultTimes(&gameInfo, &szICon, false)
				gameInfo.CurFreeRoundNum++
				// log.Println("结果 ", gameInfo.Result, "当前免费次数 ",gameInfo.CurFreeRoundNum)
				if gameInfo.CurFreeRoundNum >= gameInfo.FreeCount {
					break
				}
			}
		}
		if gameInfo.Result == 0 {
			continue
		}
		log.Println("结果 ", gameInfo.Result, "总免费次数 ", gameInfo.FreeCount)
		if multerRateMap != nil {
			if _, exists := multerRateMap[gameInfo.Result]; !exists {
				continue
			}
		}
		var strResultBuilder strings.Builder
		strResultBuilder.Reset()

		for y := 0; y < len(gameInfo.Icons); y++ {
			strResultBuilder.WriteString(fmt.Sprintf("%d,", gameInfo.Icons[y]))
		}
		strResult := strResultBuilder.String()
		if len(strResult) == 0 {
			continue
		}
		strResult = strResult[:len(strResult)-1]
		if _, exists := mMapStr[strResult]; exists {
			continue
		}
		mMapStr[strResult] = 1
		// log.Println("结果 ", gameInfo.Result, "图标个数 ", len(gameInfo.Icons))
		if gameInfo.Result > 0 {
			if gameInfo.Result <= 20 {
				if len(mMapResult[gameInfo.Result]) <= 200 {
					mMapResult[gameInfo.Result] = append(mMapResult[gameInfo.Result], strResult)
				} else {
					b1 = true
				}
			} else if gameInfo.Result > 20 && gameInfo.Result <= 50 {
				if len(mMapResult[gameInfo.Result]) <= 100 {
					mMapResult[gameInfo.Result] = append(mMapResult[gameInfo.Result], strResult)
				} else {
					b2 = true
				}
			} else {
				if len(mMapResult[gameInfo.Result]) <= 50 {
					mMapResult[gameInfo.Result] = append(mMapResult[gameInfo.Result], strResult)
				} else {
					b3 = true
				}
			}

		}
		b1 = true
		b2 = true
		b3 = true
		if b1 && b2 && b3 {
			break
		}
	}
	log.Println("完成", b1, b2, b3)
	log.Println("数据 ", len(mMapResult))

	// 保存 mMapResult 到 JSON 文件
	filename := "game_result_data.json"
	if err := saveMapResultToJSON(mMapResult, filename); err != nil {
		log.Printf("保存 JSON 文件失败: %v", err)
	}

	//write_normal_data_record_to_redis(mMapResult)

}
