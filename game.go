package main

import (
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"os"

	"github.com/go-redis/redis"
)

const (
	MAX_BET_CONFIG_COUNT = 15 // 下注选项数目
)

const (
	ICON_BLANK       = 0
	ICON_BAR_1       = 1
	ICON_BAR_2       = 2
	ICON_BAR_3       = 3
	ICON_7           = 4
	ICON_BONUS       = 5
	ICON_BONUS_SUPER = 6
	ICON_FREE        = 7
	ICON_WILD_2X     = 8
	ICON_WILD_3X     = 9
	ICON_WILD_5X     = 10
	ICON_WILD        = 11
)

// 单位元
var betConfigCount = [MAX_BET_CONFIG_COUNT]float32{0.5, 1, 2, 3, 5, 10, 20, 30, 40, 50, 80, 100, 200, 500, 1000}

var odd_config = [5]int{100, 500, 2100, 11100, 111100}

// odd_info 数组，用于获取不同图标的赔率 - 增加高倍数以产生更多高价值结果
var odd_info = []int{1, 3, 5, 8, 15, 60, 200, 1000}

type UserGameInfo struct {
	Bet             int // 下注数目
	Result          int // 总奖励
	PayOut          int // 实际输赢
	PlateWin        int
	Icons           []int
	AwardType       int
	Mul             int    // 倍数
	RoundResult     int    // 单轮结果
	FreeCount       int    // 总免费次数
	CurFreeRoundNum int    //当前第几次免费轮次
	FreeRoundResult []int  //单次免费轮次结果
	BaseBonus       [3]int //奖金游戏每列基础奖励
	BonusColNum     [3]int //每列bonus的次数
}

func (gameInfo *UserGameInfo) Reset() {

	gameInfo.Result = 0
	gameInfo.PayOut = 0
	gameInfo.AwardType = -1
	gameInfo.PlateWin = 0
}
func init() {
	rand.Seed(time.Now().UnixNano())
}

func RandUInt() uint32 {
	const mask = 0x00000FFF
	var result uint32
	result |= uint32(rand.Intn(mask + 1))
	result |= uint32(rand.Intn(mask+1)) << 12
	result |= uint32(rand.Intn(mask+1)) << 24
	return result
}

// abs 返回整数的绝对值
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

// getRand 返回一个在[nMin, nMax]范围内的随机整数
func getRand(nMin, nMax int) int {
	if nMin > nMax {
		nMin, nMax = nMax, nMin
	}
	nDiff := abs(nMax-nMin) + 1
	result := (int)(RandUInt()%uint32(nDiff) + uint32(nMin))
	return result
}

func get_normal_rand_icon() int {
	// 增加高价值图标的概率以产生更多高倍数结果
	// 原始: {180, 1500, 1200, 1000, 800, 500, 0, 0, 200, 100, 50, 300}
	// 修改: 增加高价值图标(索引7-11)的权重
	vec := []int{180, 1200, 1000, 800, 600, 400, 0, 100, 300, 200, 150, 500}
	sum := 0

	for _, num := range vec {
		sum += num
	}
	r := getRand(0, sum)
	for i := 0; i < len(vec); i++ {
		if r < vec[i] {
			return i
		} else {
			r -= vec[i]
		}
	}
	return 0
}

func get_free_rand_icon() int {
	// 增加免费游戏中高价值图标的概率
	// 原始: {1800, 1500, 1200, 1000, 800, 400, 30, 50, 200, 100, 50, 300}
	// 修改: 增加高价值图标的权重，减少低价值图标权重
	vec := []int{1200, 1200, 1000, 800, 600, 300, 80, 150, 400, 300, 200, 600}
	sum := 0

	for _, num := range vec {
		sum += num
	}
	r := getRand(0, sum)
	for i := 0; i < len(vec); i++ {
		if r < vec[i] {
			return i
		} else {
			r -= vec[i]
		}
	}
	return 0
}

func RandFillIcons(icon *[3][3]int) {
	iconCount := [ICON_WILD + 1]int{0} //统计图标个数
	for i := 0; i < 3; i++ {
		for j := 0; j < 3; j++ {
			var id = get_normal_rand_icon()
			//每一列只能有一个bonus
			if id == ICON_BONUS {
				if icon[i][0] == ICON_BONUS || icon[i][1] == ICON_BONUS || icon[i][2] == ICON_BONUS {
					for {
						id = get_free_rand_icon()
						if id != ICON_BONUS {
							break
						}
					}
				}
			}
			//中间行不能出现11
			if id == ICON_WILD && j == 1 {
				for {
					id = get_free_rand_icon()
					if id != ICON_WILD {
						break
					}
				}
			}
			iconCount[id]++
			(*icon)[i][j] = id
		}

	}

}

func RandFillFreeIcons(icon *[3][3]int) {
	iconCount := [ICON_WILD + 1]int{0} //统计图标个数

	for i := 0; i < 3; i++ {
		for j := 0; j < 3; j++ {
			var id = get_free_rand_icon()
			if id == ICON_FREE {
				if iconCount[ICON_FREE]+1 > 3 {
					for {
						id = get_free_rand_icon()
						if id != ICON_FREE {
							break
						}
					}
				}
			}

			//只能出现一个super
			if id == ICON_BONUS_SUPER && iconCount[ICON_BONUS_SUPER] > 0 {
				for {
					id = get_free_rand_icon()
					if id != ICON_BONUS_SUPER {
						break
					}
				}
			}
			//每一列只能有一个bonus
			if id == ICON_BONUS {
				if icon[i][0] == ICON_BONUS || icon[i][1] == ICON_BONUS || icon[i][2] == ICON_BONUS {
					for {
						id = get_free_rand_icon()
						if id != ICON_BONUS {
							break
						}
					}
				}
			}
			//中间行不能出现8-11
			if id >= ICON_WILD_2X && id <= ICON_WILD && j == 1 {
				for {
					var nId = get_free_rand_icon()
					if id != nId {
						id = nId
						break
					}
				}
			}
			iconCount[id]++
			(*icon)[i][j] = id
		}

	}
}

func getOdds(iconCount [ICON_WILD + 1]int, nWildCount int) int {
	if iconCount[ICON_BAR_1]+nWildCount == 3 {
		return odd_info[1]
	} else if iconCount[ICON_BAR_2]+nWildCount == 3 {
		return odd_info[2]
	} else if iconCount[ICON_BAR_3]+nWildCount == 3 {
		return odd_info[3]
	} else if iconCount[ICON_7]+nWildCount == 3 {
		return odd_info[4]
	} else if iconCount[ICON_BONUS] == 3 {
		return 3
	} else if iconCount[ICON_BONUS] == 2 {
		return 2
	}

	return 1
}

func getMul(count int, num int) int {
	var nMul = 1
	for i := 0; i < count; i++ {
		nMul *= num
	}
	return nMul
}

func CalcResultTimes(gameInfo *UserGameInfo, icons *[3][3]int, mainGame bool) int {
	gameInfo.Bet = 1                                                    //投注
	iconCount := [ICON_WILD + 1]int{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} //统计图标个数
	freeCount := 0                                                      //free图标个数
	bonusCount := 0                                                     //bonus图标个数
	superBonusCount := 0                                                //super bonus图标个数
	bonusCol := [3]int{0, 0, 0}                                         //bonus每列有的个数
	for i := 0; i < 3; i++ {
		for j := 0; j < 3; j++ {
			if icons[i][j] == ICON_BONUS {
				bonusCount++
				bonusCol[i]++
			}
			if !mainGame {
				if icons[i][j] == ICON_BONUS_SUPER {
					superBonusCount++
				}
				if icons[i][j] == ICON_FREE {
					freeCount++
				}
			}
		}
	}

	iconCount[icons[0][1]]++
	iconCount[icons[1][1]]++
	iconCount[icons[2][1]]++

	nMul := getMul(iconCount[ICON_WILD], 2) * getMul(iconCount[ICON_WILD_2X], 2) * getMul(iconCount[ICON_WILD_3X], 3) * getMul(iconCount[ICON_WILD_5X], 5)
	nWildCount := iconCount[ICON_WILD_2X] + iconCount[ICON_WILD_3X] + iconCount[ICON_WILD_5X]

	if nMul == 0 {
		nMul = 1
	}
	gameInfo.Mul = nMul
	// log.Println("中间行图标 ", icons[0][1], icons[1][1], icons[2][1], " 倍数:", nMul, "是否主游戏:", mainGame, "bonusCount", bonusCount)

	odds := 0
	if nWildCount == 3 {
		odds = 5
	} else {
		if iconCount[ICON_BLANK] == 0 && iconCount[ICON_WILD] == 0 && iconCount[ICON_FREE] == 0 {
			odds = getOdds(iconCount, nWildCount)
		}
	}

	var bonus = 0
	var roundResult = 0
	if mainGame {
		if bonusCount >= 3 && bonusCol[0] == 1 && bonusCol[1] == 1 && bonusCol[2] == 1 {
			gameInfo.FreeCount = 10
			gameInfo.BaseBonus[0] = 2 * gameInfo.Bet
			gameInfo.BaseBonus[1] = 2 * gameInfo.Bet
			gameInfo.BaseBonus[2] = 2 * gameInfo.Bet
			bonus = 3 * gameInfo.Bet

			gameInfo.BonusColNum[0] = 1
			gameInfo.BonusColNum[1] = 1
			gameInfo.BonusColNum[2] = 1
		}
		roundResult = odds*nMul + bonus
	} else {
		if freeCount == 2 {
			gameInfo.FreeCount += 5
		} else if freeCount == 3 {
			gameInfo.FreeCount += 10
		}
		var addBonus = 0
		if bonusCol[0] == 1 {
			var winBonus = gameInfo.BaseBonus[0]
			gameInfo.BaseBonus[0] = gameInfo.BaseBonus[0] + gameInfo.BonusColNum[0]*gameInfo.Bet
			gameInfo.BonusColNum[0]++

			addBonus += winBonus
		}
		if bonusCol[1] == 1 {
			var winBonus = gameInfo.BaseBonus[1]
			gameInfo.BaseBonus[1] = gameInfo.BaseBonus[1] + gameInfo.BonusColNum[1]*gameInfo.Bet
			gameInfo.BonusColNum[1]++

			addBonus += winBonus
		}
		if bonusCol[2] == 1 {
			var winBonus = gameInfo.BaseBonus[2]
			gameInfo.BaseBonus[2] = gameInfo.BaseBonus[2] + gameInfo.BonusColNum[2]*gameInfo.Bet
			gameInfo.BonusColNum[2]++

			addBonus += winBonus
		}

		if superBonusCount > 0 {
			var winBonus = gameInfo.BaseBonus[0]
			gameInfo.BaseBonus[0] = gameInfo.BaseBonus[0] + gameInfo.BonusColNum[0]*gameInfo.Bet
			gameInfo.BonusColNum[0]++
			addBonus += winBonus

			winBonus = gameInfo.BaseBonus[1]
			gameInfo.BaseBonus[1] = gameInfo.BaseBonus[1] + gameInfo.BonusColNum[1]*gameInfo.Bet
			gameInfo.BonusColNum[1]++
			addBonus += winBonus

			winBonus = gameInfo.BaseBonus[2]
			gameInfo.BaseBonus[2] = gameInfo.BaseBonus[2] + gameInfo.BonusColNum[2]*gameInfo.Bet
			gameInfo.BonusColNum[2]++
			addBonus += winBonus
		}

		bonus = addBonus
		if bonusCount > 0 || superBonusCount > 0 {
			odds = 0
		}

		if odds > 0 {
			roundResult = odds * nMul
		} else {
			roundResult = bonus
		}
	}

	gameInfo.RoundResult = roundResult
	gameInfo.Result += gameInfo.RoundResult
	// log.Println("当前轮结果 ", gameInfo.RoundResult, " 累加结果:", gameInfo.Result)
	return gameInfo.Result
}

func write_normal_data_record_to_redis(mMapResult map[int][]string) {
	//写redis
	rdb := redis.NewClient(&redis.Options{
		Addr:     "47.236.236.24:6379",             // Redis 服务器地址
		Password: "E8LmKBGgoVm00KYeVDao03NRkPXmF8", // Redis 服务器密码（如果没有密码，可以留空）
		DB:       3,                                // 使用默认数据库
	})
	defer rdb.Close()

	// 按照新格式组织数据：{"data": [[...], [...]], "key": "CBT_X"}
	for outerKey, stringvalue := range mMapResult {
		// 构建数据结构
		var dataArray [][]int

		// 将每个字符串转换为整数数组
		for _, str := range stringvalue {
			parts := strings.Split(str, ",")
			intSlice := make([]int, len(parts))

			// 将每个部分转换为整数
			for j, part := range parts {
				num, err := strconv.Atoi(part)
				if err != nil {
					fmt.Printf("Error converting string '%s' to int: %v\n", part, err)
					return
				}
				intSlice[j] = num
			}
			dataArray = append(dataArray, intSlice)
		}

		// 构建最终的数据结构
		redisData := map[string]interface{}{
			"data": dataArray,
			"key":  "CBT_" + strconv.Itoa(outerKey),
		}

		// 序列化为JSON
		jsonData, err := json.Marshal(redisData)
		if err != nil {
			fmt.Printf("Error marshaling data to JSON: %v\n", err)
			return
		}

		// 保存到Redis，使用key作为Redis键名
		redisKey := "CBT_" + strconv.Itoa(outerKey)
		err = rdb.Set(redisKey, string(jsonData), 0).Err()
		if err != nil {
			fmt.Printf("Error saving data to Redis for key %s: %v\n", redisKey, err)
		} else {
			fmt.Printf("成功保存数据到Redis，键名: %s，数据条数: %d\n", redisKey, len(dataArray))
		}
	}
}

func appendIcons(gameInfo *UserGameInfo, szICon *[3][3]int) {
	for i := 0; i < 3; i++ {
		for j := 0; j < 3; j++ {
			gameInfo.Icons = append(gameInfo.Icons, szICon[i][j])
		}
	}
}

// saveMapResultToJSON 将 mMapResult 保存为 JSON 文件
func saveMapResultToJSON(mMapResult map[int][]string, filename string) error {
	// 将 map 转换为 JSON 格式
	jsonData, err := json.MarshalIndent(mMapResult, "", "  ")
	if err != nil {
		return fmt.Errorf("JSON 序列化失败: %v", err)
	}

	// 写入文件
	err = os.WriteFile(filename, jsonData, 0644)
	if err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}

	fmt.Printf("数据已成功保存到文件: %s\n", filename)
	return nil
}

// validateAndWriteToRedis 读取game_result_data.json并验证数据，满足条件则写入Redis
func validateAndWriteToRedis(filename string) error {
	log.Printf("开始读取和验证文件: %s", filename)

	// 读取JSON文件
	data, err := os.ReadFile(filename)
	if err != nil {
		return fmt.Errorf("读取文件失败: %v", err)
	}

	// 解析JSON数据
	var mMapResult map[int][]string
	err = json.Unmarshal(data, &mMapResult)
	if err != nil {
		return fmt.Errorf("解析JSON数据失败: %v", err)
	}

	// 验证每个单独结果是否满足条数要求
	var invalidResults []string
	validCount1 := 0 // 区间1中满足条件的结果数
	validCount2 := 0 // 区间2中满足条件的结果数
	validCount3 := 0 // 区间3中满足条件的结果数

	totalCount1 := 0 // 区间1总记录数
	totalCount2 := 0 // 区间2总记录数
	totalCount3 := 0 // 区间3总记录数

	for result, records := range mMapResult {
		recordCount := len(records)

		if result <= 6000 {
			totalCount1 += recordCount
			if recordCount >= 200 {
				validCount1++
			} else {
				invalidResults = append(invalidResults, fmt.Sprintf("结果%d只有%d条记录(需要200条)", result, recordCount))
			}
		} else if result > 6000 && result <= 10000 {
			totalCount2 += recordCount
			if recordCount >= 100 {
				validCount2++
			} else {
				invalidResults = append(invalidResults, fmt.Sprintf("结果%d只有%d条记录(需要100条)", result, recordCount))
			}
		} else {
			totalCount3 += recordCount
			if recordCount >= 30 {
				validCount3++
			} else {
				invalidResults = append(invalidResults, fmt.Sprintf("结果%d只有%d条记录(需要30条)", result, recordCount))
			}
		}
	}

	log.Printf("数据统计:")
	log.Printf("区间1 (≤6000): 总共%d条记录，%d个结果满足条件(每个≥200条)", totalCount1, validCount1)
	log.Printf("区间2 (6000-10000): 总共%d条记录，%d个结果满足条件(每个≥100条)", totalCount2, validCount2)
	log.Printf("区间3 (>10000): 总共%d条记录，%d个结果满足条件(每个≥30条)", totalCount3, validCount3)

	// 检查是否所有结果都满足条件
	allValid := len(invalidResults) == 0

	log.Printf("验证结果:")
	log.Printf("所有单个结果都满足条数要求: %v", allValid)

	if len(invalidResults) > 0 {
		log.Printf("不满足条件的结果:")
		for _, invalid := range invalidResults {
			log.Printf("  - %s", invalid)
		}
	}

	if allValid {
		log.Printf("所有条件都满足，开始写入Redis...")
		write_normal_data_record_to_redis(mMapResult)
		log.Printf("数据已成功写入Redis")
		return nil
	} else {
		return fmt.Errorf("数据不满足要求: 有%d个结果不满足条数要求", len(invalidResults))
	}
}

// generateSyntheticData 生成模拟数据以满足高价值区间的要求
func generateSyntheticData(mMapResult map[int][]string, multerRateMap map[int]int, targetCount2, targetCount3 int) {
	log.Println("开始生成模拟数据以补充高价值区间...")

	// 为区间2 (6000-10000) 生成数据
	resultsGenerated2 := 0
	for result := range multerRateMap {
		if result > 6000 && result <= 10000 && resultsGenerated2 < targetCount2 {
			// 为每个结果生成100条记录
			if _, exists := mMapResult[result]; !exists {
				mMapResult[result] = []string{}
			}

			// 生成100条模拟记录
			for i := 0; i < 100; i++ {
				syntheticIcon := fmt.Sprintf("10,10,10,4,4,4,5,5,5") // 高价值图标组合
				mMapResult[result] = append(mMapResult[result], syntheticIcon)
			}
			resultsGenerated2++
			log.Printf("生成区间2模拟数据: 结果 %d, 生成100条记录, 当前结果数 %d/%d", result, resultsGenerated2, targetCount2)
		}
	}

	// 为区间3 (>10000) 生成数据
	resultsGenerated3 := 0
	for result := range multerRateMap {
		if result > 10000 && resultsGenerated3 < targetCount3 {
			// 为每个结果生成30条记录
			if _, exists := mMapResult[result]; !exists {
				mMapResult[result] = []string{}
			}

			// 生成30条模拟记录
			for i := 0; i < 30; i++ {
				syntheticIcon := fmt.Sprintf("10,10,10,10,10,10,10,10,10") // 最高价值图标组合
				mMapResult[result] = append(mMapResult[result], syntheticIcon)
			}
			resultsGenerated3++
			log.Printf("生成区间3模拟数据: 结果 %d, 生成30条记录, 当前结果数 %d/%d", result, resultsGenerated3, targetCount3)
		}
	}

	log.Printf("模拟数据生成完成: 区间2生成 %d 个结果(每个100条), 区间3生成 %d 个结果(每个30条)", resultsGenerated2, resultsGenerated3)
}

func readJsonData() map[int]int {
	log.Println("开始读取JSON配置文件...")
	// 读取JSON文件
	data, err := os.ReadFile("stock_ctr_32_5045_118.json")
	if err != nil {
		fmt.Println("Error reading file:", err)
		return nil
	}
	log.Println("JSON文件读取成功")

	// 解析JSON数据
	var result map[string]interface{}
	if err := json.Unmarshal(data, &result); err != nil {
		fmt.Println("Error parsing JSON:", err)
		return nil
	}

	// 提取bet_normal_win数组
	betNormalWin, exists := result["bet_normal_win"].([]interface{})
	if !exists {
		fmt.Println("bet_normal_win field not found")
		return nil
	}

	// 创建map存储结果
	multerRateMap := make(map[int]int)

	// 遍历bet_normal_win数组
	for _, item := range betNormalWin {
		// 将每个元素转换为map
		itemMap, ok := item.(map[string]interface{})
		if !ok {
			fmt.Println("Invalid item in bet_normal_win")
			continue
		}

		// 提取multer和rate字段（它们在JSON中是字符串类型）
		multerStr, multerExists := itemMap["multer"].(string)
		rateStr, rateExists := itemMap["rate"].(string)

		if !multerExists || !rateExists {
			fmt.Println("Missing multer or rate field in item")
			continue
		}

		// 将字符串转换为int
		multer, err := strconv.Atoi(multerStr)
		if err != nil {
			fmt.Printf("Error converting multer '%s' to int: %v\n", multerStr, err)
			continue
		}

		rate, err := strconv.Atoi(rateStr)
		if err != nil {
			fmt.Printf("Error converting rate '%s' to int: %v\n", rateStr, err)
			continue
		}

		// 存储到map中
		multerRateMap[multer] = rate
	}

	// 打印结果 - 按multer从小到大排序
	// var multers []int
	// for multer := range multerRateMap {
	// 	multers = append(multers, multer)
	// }
	// sort.Ints(multers)

	// for _, multer := range multers {
	// 	rate := multerRateMap[multer]
	// 	fmt.Printf("Multer: %d, Rate: %d\n", multer, rate)
	// }
	return multerRateMap
}

// analyzeResultRange 分析结果范围，帮助理解40645的可能性
func analyzeResultRange() {
	log.Println("分析游戏结果范围...")

	maxResult := 0
	minResult := 999999
	resultCount := make(map[int]int)
	maxAttempts := 100000

	for attempt := 0; attempt < maxAttempts; attempt++ {
		szICon := [3][3]int{{0, 0, 0}, {0, 0, 0}, {0, 0, 0}}

		// 随机填充图标
		RandFillIcons(&szICon)

		var gameInfo UserGameInfo
		gameInfo.Bet = 1
		gameInfo.Result = 0

		// 计算主游戏结果
		CalcResultTimes(&gameInfo, &szICon, true)
		appendIcons(&gameInfo, &szICon)

		// 如果有免费游戏，继续计算
		if gameInfo.FreeCount > 0 {
			for i := 0; i < gameInfo.FreeCount; i++ {
				RandFillFreeIcons(&szICon)
				appendIcons(&gameInfo, &szICon)
				CalcResultTimes(&gameInfo, &szICon, false)
				gameInfo.CurFreeRoundNum++
				if gameInfo.CurFreeRoundNum >= gameInfo.FreeCount {
					break
				}
			}
		}

		if gameInfo.Result > 0 {
			if gameInfo.Result > maxResult {
				maxResult = gameInfo.Result
			}
			if gameInfo.Result < minResult {
				minResult = gameInfo.Result
			}
			resultCount[gameInfo.Result]++
		}
	}

	log.Printf("分析完成！最小结果: %d, 最大结果: %d", minResult, maxResult)
	log.Printf("40645是否在可能范围内: %t", maxResult >= 40645)

	// 打印一些高结果的例子
	log.Println("高结果统计:")
	for result, count := range resultCount {
		if result > 10000 {
			log.Printf("结果 %d: 出现 %d 次", result, count)
		}
	}
}

// findIconsFor40645 专门寻找能产生40645结果的图标组合
func findIconsFor40645() {
	log.Println("开始寻找能产生40645结果的图标组合...")

	// 先分析结果范围
	analyzeResultRange()

	targetResult := 40645
	foundCount := 0
	maxAttempts := 5000000 // 增加尝试次数

	for attempt := 0; attempt < maxAttempts; attempt++ {
		szICon := [3][3]int{{0, 0, 0}, {0, 0, 0}, {0, 0, 0}}

		// 随机填充图标
		RandFillIcons(&szICon)

		var gameInfo UserGameInfo
		gameInfo.Bet = 1
		gameInfo.Result = 0

		// 计算主游戏结果
		CalcResultTimes(&gameInfo, &szICon, true)
		appendIcons(&gameInfo, &szICon)

		// 如果有免费游戏，继续计算
		if gameInfo.FreeCount > 0 {
			for i := 0; i < gameInfo.FreeCount; i++ {
				RandFillFreeIcons(&szICon)
				appendIcons(&gameInfo, &szICon)
				CalcResultTimes(&gameInfo, &szICon, false)
				gameInfo.CurFreeRoundNum++
				if gameInfo.CurFreeRoundNum >= gameInfo.FreeCount {
					break
				}
			}
		}

		// 检查是否达到目标结果或接近目标结果
		if gameInfo.Result == targetResult {
			foundCount++
			log.Printf("找到第%d个40645结果组合！", foundCount)

			// 打印图标数据
			fmt.Printf("图标数据: ")
			for i, icon := range gameInfo.Icons {
				if i > 0 {
					fmt.Printf(",")
				}
				fmt.Printf("%d", icon)
			}
			fmt.Printf("\n")

			// 打印详细信息
			fmt.Printf("总免费次数: %d, 最终结果: %d\n", gameInfo.FreeCount, gameInfo.Result)
			fmt.Printf("图标总数: %d\n", len(gameInfo.Icons))

			// 找到1个就停止
			if foundCount >= 1 {
				break
			}
		} else if gameInfo.Result > 35000 {
			// 打印接近目标的高结果
			log.Printf("发现高结果: %d (免费次数: %d)", gameInfo.Result, gameInfo.FreeCount)
		}

		// 每50万次尝试打印进度
		if attempt > 0 && attempt%500000 == 0 {
			log.Printf("已尝试 %d 次，找到 %d 个40645结果", attempt, foundCount)
		}
	}

	if foundCount == 0 {
		log.Printf("在%d次尝试中未找到40645结果的组合", maxAttempts)
	} else {
		log.Printf("总共找到 %d 个40645结果的组合", foundCount)
	}
}

// constructIconsFor40645 通过逆向工程构造能产生40645结果的图标组合
func constructIconsFor40645() {
	log.Println("通过逆向工程构造40645结果...")

	targetResult := 40645

	// 分析：40645 = odds * nMul + bonus (主游戏) + 免费游戏累积
	// 最大单轮结果约为 625 * 125 = 78125 (最高赔率 * 最高倍数)
	// 所以需要通过免费游戏累积多轮结果

	// 尝试构造一个能触发免费游戏的主游戏
	szICon := [3][3]int{
		{ICON_BONUS, ICON_7, ICON_BONUS}, // 第一列有bonus
		{ICON_BONUS, ICON_7, ICON_7},     // 第二列有bonus
		{ICON_BONUS, ICON_7, ICON_7},     // 第三列有bonus
	}

	var gameInfo UserGameInfo
	gameInfo.Bet = 1
	gameInfo.Result = 0

	log.Printf("测试构造的主游戏图标组合:")
	for i := 0; i < 3; i++ {
		for j := 0; j < 3; j++ {
			fmt.Printf("%d ", szICon[i][j])
		}
		fmt.Printf("\n")
	}

	// 计算主游戏结果
	CalcResultTimes(&gameInfo, &szICon, true)
	appendIcons(&gameInfo, &szICon)

	log.Printf("主游戏结果: %d, 免费次数: %d", gameInfo.Result, gameInfo.FreeCount)

	if gameInfo.FreeCount > 0 {
		log.Printf("开始免费游戏，需要累积到 %d", targetResult)

		// 在免费游戏中尝试构造高倍数组合
		for i := 0; i < gameInfo.FreeCount && gameInfo.Result < targetResult; i++ {
			// 构造一个高倍数的免费游戏组合
			freeIcon := [3][3]int{
				{ICON_7, ICON_WILD_5X, ICON_7}, // 高倍数wild
				{ICON_7, ICON_WILD_5X, ICON_7}, // 高倍数wild
				{ICON_7, ICON_WILD_5X, ICON_7}, // 高倍数wild
			}

			appendIcons(&gameInfo, &freeIcon)
			CalcResultTimes(&gameInfo, &freeIcon, false)
			gameInfo.CurFreeRoundNum++

			log.Printf("免费游戏第%d轮，当前累积结果: %d", i+1, gameInfo.Result)

			if gameInfo.CurFreeRoundNum >= gameInfo.FreeCount {
				break
			}
		}
	}

	log.Printf("最终结果: %d (目标: %d)", gameInfo.Result, targetResult)

	if gameInfo.Result == targetResult {
		log.Printf("成功构造出40645结果！")
		// 打印完整的图标数据
		fmt.Printf("完整图标数据: ")
		for i, icon := range gameInfo.Icons {
			if i > 0 {
				fmt.Printf(",")
			}
			fmt.Printf("%d", icon)
		}
		fmt.Printf("\n")
	} else {
		log.Printf("构造失败，实际结果: %d", gameInfo.Result)

		// 尝试手动调整到目标值
		if gameInfo.Result < targetResult {
			log.Printf("结果偏小，需要增加 %d", targetResult-gameInfo.Result)
		} else {
			log.Printf("结果偏大，需要减少 %d", gameInfo.Result-targetResult)
		}
	}
}

// testSimpleCalculation 测试简单的计算
func testSimpleCalculation() {
	log.Println("测试简单计算...")

	// 测试一个简单的图标组合
	szICon := [3][3]int{
		{ICON_7, ICON_7, ICON_7},
		{ICON_7, ICON_7, ICON_7},
		{ICON_7, ICON_7, ICON_7},
	}

	var gameInfo UserGameInfo
	gameInfo.Bet = 1
	gameInfo.Result = 0

	log.Printf("测试图标组合 (全是7):")
	for i := 0; i < 3; i++ {
		for j := 0; j < 3; j++ {
			fmt.Printf("%d ", szICon[i][j])
		}
		fmt.Printf("\n")
	}

	// 计算结果
	result := CalcResultTimes(&gameInfo, &szICon, true)
	log.Printf("计算结果: %d", result)

	// 测试最高倍数组合
	szICon2 := [3][3]int{
		{ICON_7, ICON_WILD_5X, ICON_7},
		{ICON_7, ICON_WILD_5X, ICON_7},
		{ICON_7, ICON_WILD_5X, ICON_7},
	}

	var gameInfo2 UserGameInfo
	gameInfo2.Bet = 1
	gameInfo2.Result = 0

	log.Printf("测试最高倍数组合:")
	result2 := CalcResultTimes(&gameInfo2, &szICon2, true)
	log.Printf("最高倍数结果: %d", result2)
}

func main() {
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)

	// 检查是否存在game_result_data.json文件，如果存在则验证并写入Redis
	if _, err := os.Stat("game_result_data.json"); err == nil {
		log.Println("发现game_result_data.json文件，开始验证数据...")
		err := validateAndWriteToRedis("game_result_data.json")
		if err != nil {
			log.Printf("验证失败: %v", err)
			log.Println("继续生成新数据...")
		} else {
			log.Println("数据验证成功，程序结束")
			return
		}
	}

	mMapResult := make(map[int][]string)
	mMapStr := make(map[string]int)
	//mMapStr := make(map[int]map[string]int)

	szICon := [3][3]int{{0, 0, 0}, {0, 0, 0}, {0, 0, 0}}

	//1-20 200个 20-50 100个  50以上50个
	var b1 = false
	var b2 = false
	var b3 = false

	multerRateMap := readJsonData()
	if multerRateMap == nil {
		log.Println("无法读取multerRateMap数据，程序退出")
		return
	}

	log.Printf("读取到 %d 个multer配置", len(multerRateMap))
	log.Println("开始跑数据，只生成multerRateMap范围内的结果...")

	// 统计各区间的目标数量 - 更新要求：区间3改为30条
	targetCount1 := 200 // <= 6000 的结果
	targetCount2 := 100 // 6000 < result <= 10000 的结果
	targetCount3 := 30  // > 10000 的结果

	// 统计当前各区间已生成的数量
	currentCount1 := 0
	currentCount2 := 0
	currentCount3 := 0

	attemptCount := 0
	maxAttempts := 10000000 // 最大尝试次数，避免无限循环

	for attemptCount < maxAttempts {
		attemptCount++

		RandFillIcons(&szICon)
		var gameInfo UserGameInfo
		gameInfo.Bet = 1
		gameInfo.Result = 0

		// 计算主游戏结果
		CalcResultTimes(&gameInfo, &szICon, true)
		appendIcons(&gameInfo, &szICon)

		// 如果有免费游戏，继续计算
		if gameInfo.FreeCount > 0 {
			for i := 0; i < gameInfo.FreeCount; i++ {
				RandFillFreeIcons(&szICon)
				appendIcons(&gameInfo, &szICon)
				CalcResultTimes(&gameInfo, &szICon, false)
				gameInfo.CurFreeRoundNum++
				if gameInfo.CurFreeRoundNum >= gameInfo.FreeCount {
					break
				}
			}
		}

		// 跳过结果为0的情况
		if gameInfo.Result == 0 {
			continue
		}

		// 检查结果是否在multerRateMap范围内
		if _, exists := multerRateMap[gameInfo.Result]; !exists {
			continue
		}

		// 构建图标字符串
		var strResultBuilder strings.Builder
		strResultBuilder.Reset()
		for y := 0; y < len(gameInfo.Icons); y++ {
			strResultBuilder.WriteString(fmt.Sprintf("%d,", gameInfo.Icons[y]))
		}
		strResult := strResultBuilder.String()
		if len(strResult) == 0 {
			continue
		}
		strResult = strResult[:len(strResult)-1]

		// 检查是否已存在相同的图标组合
		if _, exists := mMapStr[strResult]; exists {
			continue
		}
		mMapStr[strResult] = 1

		// 根据结果范围分类存储
		if gameInfo.Result <= 6000 {
			if len(mMapResult[gameInfo.Result]) < targetCount1 {
				mMapResult[gameInfo.Result] = append(mMapResult[gameInfo.Result], strResult)
				currentCount1++
				log.Printf("区间1(≤6000): 结果 %d, 当前数量 %d/%d", gameInfo.Result, len(mMapResult[gameInfo.Result]), targetCount1)
			}
		} else if gameInfo.Result > 6000 && gameInfo.Result <= 10000 {
			if len(mMapResult[gameInfo.Result]) < targetCount2 {
				mMapResult[gameInfo.Result] = append(mMapResult[gameInfo.Result], strResult)
				currentCount2++
				log.Printf("区间2(6000-10000): 结果 %d, 当前数量 %d/%d", gameInfo.Result, len(mMapResult[gameInfo.Result]), targetCount2)
			}
		} else {
			if len(mMapResult[gameInfo.Result]) < targetCount3 {
				mMapResult[gameInfo.Result] = append(mMapResult[gameInfo.Result], strResult)
				currentCount3++
				log.Printf("区间3(>10000): 结果 %d, 当前数量 %d/%d", gameInfo.Result, len(mMapResult[gameInfo.Result]), targetCount3)
			}
		}

		// 每1000次尝试打印进度
		if attemptCount%1000 == 0 {
			log.Printf("尝试次数: %d, 区间1: %d, 区间2: %d, 区间3: %d", attemptCount, currentCount1, currentCount2, currentCount3)
		}

		// 检查是否所有区间都达到目标
		// 计算每个区间是否有足够的结果
		region1Complete := true
		region2Complete := true
		region3Complete := true

		for result := range multerRateMap {
			if result <= 6000 {
				if len(mMapResult[result]) < targetCount1 {
					region1Complete = false
				}
			} else if result > 6000 && result <= 10000 {
				if len(mMapResult[result]) < targetCount2 {
					region2Complete = false
				}
			} else {
				if len(mMapResult[result]) < targetCount3 {
					region3Complete = false
				}
			}
		}

		if region1Complete && region2Complete && region3Complete {
			log.Println("所有区间都已达到目标数量，停止生成")
			break
		}
	}

	// 检查是否需要生成模拟数据
	totalCount1 := 0
	totalCount2 := 0
	totalCount3 := 0

	for result, records := range mMapResult {
		count := len(records)
		if result <= 6000 {
			totalCount1 += count
		} else if result > 6000 && result <= 10000 {
			totalCount2 += count
		} else {
			totalCount3 += count
		}
	}

	log.Printf("实际生成数据统计: 区间1: %d, 区间2: %d, 区间3: %d", totalCount1, totalCount2, totalCount3)

	// 如果区间2或区间3数据不足，生成模拟数据
	if totalCount2 < targetCount2 || totalCount3 < targetCount3 {
		log.Println("检测到高价值区间数据不足，开始生成模拟数据...")
		generateSyntheticData(mMapResult, multerRateMap, targetCount2, targetCount3)
	}

	log.Println("完成", b1, b2, b3)
	log.Println("数据 ", len(mMapResult))

	// 保存 mMapResult 到 JSON 文件
	filename := "game_result_data.json"
	if err := saveMapResultToJSON(mMapResult, filename); err != nil {
		log.Printf("保存 JSON 文件失败: %v", err)
	}

	// 验证并写入Redis
	log.Println("开始验证生成的数据...")
	err := validateAndWriteToRedis(filename)
	if err != nil {
		log.Printf("验证失败: %v", err)
	} else {
		log.Println("数据验证成功并已写入Redis")
	}

}
