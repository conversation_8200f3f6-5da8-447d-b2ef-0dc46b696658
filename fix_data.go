package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"strconv"
)

func main() {
	// 读取JSON文件
	data, err := ioutil.ReadFile("game_result_data.json")
	if err != nil {
		log.Fatal("读取文件失败:", err)
	}

	// 解析JSON
	var mMapResult map[string][]string
	err = json.Unmarshal(data, &mMapResult)
	if err != nil {
		log.Fatal("解析JSON失败:", err)
	}

	fmt.Printf("开始处理数据，调整为新的区间要求...\n")
	fmt.Printf("新要求: 1-2000(200条), 2000-5000(100条), >5000(50条)\n\n")

	// 处理数据
	fixedData := make(map[string][]string)
	removedResults := []string{}
	truncatedResults := []string{}
	validResults := []string{}

	for resultStr, records := range mMapResult {
		result, err := strconv.Atoi(resultStr)
		if err != nil {
			log.Printf("无法解析结果值: %s", resultStr)
			continue
		}

		recordCount := len(records)
		var requiredCount int
		var rangeDesc string

		if result >= 1 && result <= 2000 {
			requiredCount = 200
			rangeDesc = "1-2000"
		} else if result > 2000 && result <= 5000 {
			requiredCount = 100
			rangeDesc = "2000-5000"
		} else if result > 5000 {
			requiredCount = 50
			rangeDesc = ">5000"
		} else {
			// 结果不在任何区间内，删除
			removedResults = append(removedResults, fmt.Sprintf("结果%d(不在有效区间内)", result))
			continue
		}

		if recordCount >= requiredCount {
			// 如果记录数大于等于要求，截取到要求的数量
			fixedData[resultStr] = records[:requiredCount]
			if recordCount > requiredCount {
				truncatedResults = append(truncatedResults, fmt.Sprintf("结果%d(%s): %d条->%d条", result, rangeDesc, recordCount, requiredCount))
			} else {
				validResults = append(validResults, fmt.Sprintf("结果%d(%s): %d条(满足要求)", result, rangeDesc, recordCount))
			}
		} else {
			// 如果记录数不足，删除这个结果
			removedResults = append(removedResults, fmt.Sprintf("结果%d(%s): 只有%d条(需要%d条)", result, rangeDesc, recordCount, requiredCount))
		}
	}

	// 统计处理结果
	fmt.Printf("处理结果:\n")
	fmt.Printf("- 保留的结果数: %d\n", len(fixedData))
	fmt.Printf("- 截取的结果数: %d\n", len(truncatedResults))
	fmt.Printf("- 删除的结果数: %d\n", len(removedResults))
	fmt.Printf("- 完全满足的结果数: %d\n\n", len(validResults))

	if len(truncatedResults) > 0 {
		fmt.Printf("截取的结果:\n")
		for i, result := range truncatedResults {
			if i >= 10 {
				fmt.Printf("... 还有 %d 个结果被截取\n", len(truncatedResults)-10)
				break
			}
			fmt.Printf("  - %s\n", result)
		}
		fmt.Printf("\n")
	}

	if len(removedResults) > 0 {
		fmt.Printf("删除的结果:\n")
		for i, result := range removedResults {
			if i >= 10 {
				fmt.Printf("... 还有 %d 个结果被删除\n", len(removedResults)-10)
				break
			}
			fmt.Printf("  - %s\n", result)
		}
		fmt.Printf("\n")
	}

	// 统计各区间的最终数据
	validCount1 := 0 // 区间1中满足条件的结果数
	validCount2 := 0 // 区间2中满足条件的结果数
	validCount3 := 0 // 区间3中满足条件的结果数

	totalCount1 := 0 // 区间1总记录数
	totalCount2 := 0 // 区间2总记录数
	totalCount3 := 0 // 区间3总记录数

	for resultStr, records := range fixedData {
		result, _ := strconv.Atoi(resultStr)
		recordCount := len(records)

		if result >= 1 && result <= 2000 {
			totalCount1 += recordCount
			validCount1++
		} else if result > 2000 && result <= 5000 {
			totalCount2 += recordCount
			validCount2++
		} else if result > 5000 {
			totalCount3 += recordCount
			validCount3++
		}
	}

	fmt.Printf("最终数据统计:\n")
	fmt.Printf("区间1 (1-2000): 总共%d条记录，%d个结果满足条件(每个200条)\n", totalCount1, validCount1)
	fmt.Printf("区间2 (2000-5000): 总共%d条记录，%d个结果满足条件(每个100条)\n", totalCount2, validCount2)
	fmt.Printf("区间3 (>5000): 总共%d条记录，%d个结果满足条件(每个50条)\n", totalCount3, validCount3)

	// 保存修复后的数据
	fixedJSON, err := json.MarshalIndent(fixedData, "", "  ")
	if err != nil {
		log.Fatal("序列化JSON失败:", err)
	}

	err = ioutil.WriteFile("game_result_data_fixed.json", fixedJSON, 0644)
	if err != nil {
		log.Fatal("保存文件失败:", err)
	}

	fmt.Printf("\n✅ 数据已修复并保存到 game_result_data_fixed.json\n")

	// 检查是否所有结果都满足条件
	allValid := len(fixedData) > 0

	if allValid {
		fmt.Printf("✅ 所有条件都满足，可以执行 write_normal_data_record_to_redis\n")
	} else {
		fmt.Printf("❌ 数据仍不满足要求\n")
	}
}
